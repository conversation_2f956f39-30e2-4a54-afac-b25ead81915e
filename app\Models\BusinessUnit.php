<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BusinessUnit extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'status',
        'order',
        'contact_info',
        'social_media',
        'primary_color',
    ];

    protected function casts(): array
    {
        return [
            'contact_info' => 'array',
            'social_media' => 'array',
            'order' => 'integer',
        ];
    }

    public function pages(): HasMany
    {
        return $this->hasMany(BusinessUnitPage::class);
    }

    public function galleries(): HasMany
    {
        return $this->hasMany(BusinessUnitGallery::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isComingSoon(): bool
    {
        return $this->status === 'coming_soon';
    }
}
