# Cigi Global - Company Profile Website

A comprehensive company profile website for Cigi Global, serving as a central hub to promote all business units under the Cigi Global umbrella.

## Business Units

- **PB Cigi** - Village Badminton Team
- **Cigi Net** - Internet Service Unit (with pricing pages)
- **Cigi Mart** - Retail Business Unit
- **Cigi Food** - Culinary Business Unit (with food menu/listing)
- **Cigi FC** - Football Team
- **Cigi Farm** - Agriculture Business Unit (with product catalog)
- **<PERSON>igi Archery** - Archery Team
- **KRL Cigi** - Eco-Friendly Village (coming soon)

## Technical Stack

### Backend
- **Laravel 12** - Latest PHP framework with streamlined structure
- **PHP 8.4** - Latest PHP version
- **MySQL** - Database engine
- **Inertia.js v2** - Server-side rendering with client-side navigation

### Frontend
- **React 19** - Latest React version
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS v4** - Modern utility-first CSS framework
- **Vite** - Fast build tool

### Testing & Quality
- **Pest v4** - Modern PHP testing framework with browser testing
- **Laravel Pint** - Code formatting
- **ESLint** - JavaScript/TypeScript linting

## Current Codebase Analysis

### Available Features
✅ **Authentication System** - Complete login/register/password reset  
✅ **User Management** - Basic user model with profile settings  
✅ **Layout System** - App layout with sidebar navigation  
✅ **UI Components** - Complete shadcn/ui component library  
✅ **Settings Pages** - Profile, password, appearance management  
✅ **Dark Mode** - Full dark/light theme support  
✅ **Responsive Design** - Mobile-first responsive layouts  

### Available Components
- Sidebar navigation with collapsible menu
- Breadcrumb navigation
- Form components (input, button, select, etc.)
- Card layouts and data display
- Modal dialogs and sheets
- Avatar and user menu components
- Skeleton loading states

## Development Plan

### Phase 1: Database Design & Models

#### 1.1 User Role System (Spatie Laravel Permission)
- Install and configure Spatie Laravel Permission package
- Create roles: Super Admin, Admin

- Create permissions for different operations
- Use Spatie's built-in middleware and policies

#### 1.2 Business Unit Management
```sql
-- business_units table
- id, name, slug, description, logo, status, order, created_at, updated_at

-- business_unit_pages table  
- id, business_unit_id, type (hero, about, contact, pricing, catalog, menu), 
  title, content, images, meta_data, is_active, created_at, updated_at

-- business_unit_galleries table
- id, business_unit_id, title, images, description, created_at, updated_at
```

#### 1.3 Global Content Management
```sql
-- global_settings table
- id, key, value, type, description, created_at, updated_at

-- news_articles table
- id, title, slug, content, featured_image, excerpt, status, 
  published_at, created_by, created_at, updated_at
```

### Phase 2: Authentication & Authorization

#### 2.1 Role-Based Access Control
- **Super Admin**: Full system access, can manage other admins
- **Admin**: Content management only, cannot manage other admin accounts
- Use Spatie's built-in middleware: `role:super-admin`, `permission:manage-users`
- Update existing controllers with proper authorization

#### 2.2 Enhanced User Model with Spatie
- Implement Spatie's HasRoles trait
- Define roles and permissions in seeders
- Use Spatie's helper methods: `hasRole()`, `can()`, etc.
- Update factories and seeders with role assignments

### Phase 3: Admin Dashboard

#### 3.1 Admin Navigation Structure
```
/admin
├── /dashboard (overview stats)
├── /business-units (CRUD operations)
│   ├── /create
│   ├── /{unit}/edit
│   └── /{unit}/pages (manage unit pages)
├── /news (global news management)
├── /settings (global site settings)
└── /users (super admin only)
```

#### 3.2 Business Unit Management Interface
- Create/edit business unit forms
- Page content management with rich text editor
- Image upload and gallery management
- URL slug management and preview

### Phase 4: Public Website Structure

#### 4.1 Clean URL Routing
```
/ (homepage with all business units)
/news (global news listing)
/news/{slug} (individual news article)

Business Unit Routes:
/{unit-slug} (main unit page)
/{unit-slug}/about
/{unit-slug}/contact
/{unit-slug}/gallery

Specialized Pages:
/ciginet/pricing (internet plans)
/cigifarm/products (product catalog)
/cigifood/menu (food menu)
```

#### 4.2 Dynamic Page Components
- Hero sections with customizable content
- About sections with rich text
- Contact forms with unit-specific details
- Gallery components with lightbox
- Pricing tables for Cigi Net
- Product catalogs for Cigi Farm
- Menu listings for Cigi Food

### Phase 5: Content Management Features

#### 5.1 Rich Content Editor
- Implement TinyMCE or similar for content editing
- Image upload with optimization
- SEO meta fields management
- Preview functionality

#### 5.2 Media Management
- Centralized image library
- Image resizing and optimization
- Gallery management for each business unit

### Phase 6: Advanced Features

#### 6.1 SEO Optimization
- Dynamic meta tags per page
- Sitemap generation
- Open Graph tags
- JSON-LD structured data

#### 6.2 Performance
- Image optimization and lazy loading
- Database query optimization

## File Structure Plan

### Models
```
app/Models/
├── User.php (enhanced with roles)
├── BusinessUnit.php
├── BusinessUnitPage.php
├── BusinessUnitGallery.php
├── GlobalSetting.php
└── NewsArticle.php
```

### Controllers
```
app/Http/Controllers/
├── Admin/
│   ├── DashboardController.php
│   ├── BusinessUnitController.php
│   ├── BusinessUnitPageController.php
│   ├── NewsController.php
│   ├── GlobalSettingController.php
│   └── UserController.php (super admin only)
└── Public/
    ├── HomeController.php
    ├── BusinessUnitController.php
    └── NewsController.php
```

### Frontend Pages
```
resources/js/pages/
├── admin/
│   ├── dashboard.tsx
│   ├── business-units/
│   ├── news/
│   ├── settings/
│   └── users/
└── public/
    ├── home.tsx
    ├── business-unit/
    ├── news/
    └── specialized-pages/
```

### API Endpoints (for admin)
```
/admin/api/business-units
/admin/api/business-units/{id}/pages
/admin/api/news
/admin/api/settings
/admin/api/users (super admin only)
```

## Implementation Strategy

### 1. Database First
- Create all migrations
- Set up model relationships
- Create factories and seeders

### 2. Authentication Enhancement
- Extend user system with roles
- Create middleware and policies
- Test role-based access

### 3. Admin Interface
- Build admin dashboard
- Create CRUD interfaces
- Implement content management

### 4. Public Interface
- Create dynamic routing
- Build responsive components
- Implement business unit pages

### 5. Testing & Optimization
- Write comprehensive tests
- Optimize performance
- Ensure responsive design

## Success Metrics

- ✅ Complete admin content management system
- ✅ All UI Text in indonesian language
- ✅ Dynamic business unit pages with unique content
- ✅ Role-based access control working properly
- ✅ Responsive design across all devices
- ✅ SEO optimized pages
- ✅ Fast loading times
- ✅ Comprehensive test coverage

## Next Steps

1. **Immediate**: Design and implement database schema
2. **Short-term**: Build admin authentication and basic CRUD
3. **Medium-term**: Create public-facing business unit pages
4. **Long-term**: Advanced features and optimization

This plan leverages the existing Laravel React starter kit infrastructure while building a comprehensive company profile website that meets all specified requirements.
