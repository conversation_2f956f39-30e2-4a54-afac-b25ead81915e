# 🏢 Cigi Global - Company Profile Website

A comprehensive company profile website for Cigi Global, serving as a central hub to promote all business units under the Cigi Global umbrella.

## 🚀 Features

### Modern Frontend Application
- **Contemporary Visual Design**: Modern color schemes, typography, and spacing
- **Professional Style**: Consistent design system with good visual hierarchy
- **Responsive Design**: Optimized for all device sizes with mobile-first approach
- **Smooth Animations**: AOS (Animate On Scroll) based animations and transitions
- **Glass Morphism Effects**: Modern background blur and transparency effects

### Admin System & CMS
- **Admin Dashboard**: Comprehensive admin interface for content management
- **News Management**: Complete CRUD system for articles and news
- **Business Unit Management**: Dynamic content management for all business units
- **Role-Based Access Control**: Super Admin and Admin roles with proper permissions
- **Media Library**: Advanced media management with Spatie Media Library
- **SEO Optimization**: Dynamic meta tags and structured data

## Business Units

- **PB Cigi** - Village Badminton Team
- **Cigi Net** - Internet Service Unit (with pricing pages)
- **Cigi Mart** - Retail Business Unit
- **<PERSON>igi Food** - Culinary Business Unit (with food menu/listing)
- **Cigi FC** - Football Team
- **Cigi Farm** - Agriculture Business Unit (with product catalog)
- **<PERSON><PERSON>** - Archery Team
- **KRL Cigi** - Eco-Friendly Village (coming soon)

## Technical Stack

### Backend
- **Laravel 12** - Latest PHP framework with streamlined structure
- **PHP 8.2+** - Modern PHP version with performance improvements
- **MySQL** - Database engine
- **Inertia.js v2** - Server-side rendering with client-side navigation
- **Spatie Laravel Permission v6** - Role and permission management
- **Spatie Laravel Media Library v11** - Advanced media management system

### Frontend
- **React 19** - Latest React version
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS v4** - Modern utility-first CSS framework
- **Vite** - Fast build tool

### Testing & Quality
- **Pest v4** - Modern PHP testing framework with browser testing
- **Laravel Pint** - Code formatting
- **ESLint** - JavaScript/TypeScript linting

## Current Codebase Analysis

### Available Features
✅ **Authentication System** - Complete login/register/password reset  
✅ **User Management** - Basic user model with profile settings  
✅ **Layout System** - App layout with sidebar navigation  
✅ **UI Components** - Complete shadcn/ui component library  
✅ **Settings Pages** - Profile, password, appearance management  
✅ **Dark Mode** - Full dark/light theme support  
✅ **Responsive Design** - Mobile-first responsive layouts  

### Available Components
- Sidebar navigation with collapsible menu
- Breadcrumb navigation
- Form components (input, button, select, etc.)
- Card layouts and data display
- Modal dialogs and sheets
- Avatar and user menu components
- Skeleton loading states

## Development Plan

### Phase 1: Database Design & Models

#### 1.1 User Role System (Spatie Laravel Permission)
- Install and configure Spatie Laravel Permission package
- Create roles: Super Admin, Admin

- Create permissions for different operations
- Use Spatie's built-in middleware and policies

#### 1.2 Business Unit Management
```sql
-- business_units table
- id, name, slug, description, logo, status, order, created_at, updated_at

-- business_unit_pages table  
- id, business_unit_id, type (hero, about, contact, pricing, catalog, menu), 
  title, content, images, meta_data, is_active, created_at, updated_at

-- business_unit_galleries table
- id, business_unit_id, title, images, description, created_at, updated_at

-- media table (Spatie Media Library)
- id, model_type, model_id, uuid, collection_name, name, file_name,
  mime_type, disk, conversions_disk, size, manipulations, custom_properties,
  generated_conversions, responsive_images, order_column, created_at, updated_at
```

#### 1.3 Global Content Management
```sql
-- global_settings table
- id, key, value, type, description, created_at, updated_at

-- news_articles table
- id, title, slug, content, featured_image, excerpt, status, 
  published_at, created_by, created_at, updated_at
```

### Phase 2: Authentication & Authorization

#### 2.1 Role-Based Access Control
- **Super Admin**: Full system access, can manage other admins
- **Admin**: Content management only, cannot manage other admin accounts
- Use Spatie's built-in middleware: `role:super-admin`, `permission:manage-users`
- Update existing controllers with proper authorization

#### 2.2 Enhanced User Model with Spatie
- Implement Spatie's HasRoles trait
- Define roles and permissions in seeders
- Use Spatie's helper methods: `hasRole()`, `can()`, etc.
- Update factories and seeders with role assignments

### Phase 3: Admin Dashboard

#### 3.1 Admin Navigation Structure
```
/admin
├── /dashboard (overview stats)
├── /business-units (CRUD operations)
│   ├── /create
│   ├── /{unit}/edit
│   └── /{unit}/pages (manage unit pages)
├── /news (global news management)
├── /settings (global site settings)
└── /users (super admin only)
```

#### 3.2 Business Unit Management Interface
- Create/edit business unit forms
- Page content management with rich text editor
- Image upload and gallery management
- URL slug management and preview

### Phase 4: Public Website Structure

#### 4.1 Clean URL Routing
```
/ (homepage with all business units)
/news (global news listing)
/news/{slug} (individual news article)

Business Unit Routes:
/{unit-slug} (main unit page)
/{unit-slug}/about
/{unit-slug}/contact
/{unit-slug}/gallery

Specialized Pages:
/ciginet/pricing (internet plans)
/cigifarm/products (product catalog)
/cigifood/menu (food menu)
```

#### 4.2 Dynamic Page Components
- Hero sections with customizable content
- About sections with rich text
- Contact forms with unit-specific details
- Gallery components with lightbox
- Pricing tables for Cigi Net
- Product catalogs for Cigi Farm
- Menu listings for Cigi Food

### Phase 5: Content Management Features

#### 5.1 Rich Content Editor
- Implement TinyMCE or similar for content editing
- Image upload with optimization
- SEO meta fields management
- Preview functionality

#### 5.2 Media Management (Spatie Laravel Media Library)
- **Spatie Laravel Media Library v11.14** - Centralized media management system
- **Multiple Media Collections** per model for organized file management
- **Automatic Image Processing** with responsive image generation
- **File Type Validation** and security measures
- **Media Library Interface** with search and filtering capabilities

**Media Collections for Business Units:**
- `hero_images` - Banner/hero section images
- `gallery` - Photo galleries and showcases
- `logos` - Business unit logos and branding
- `documents` - PDF catalogs, menus, price lists
- `featured_image` - Main promotional images

**Media Collections for News Articles:**
- `featured_image` - Article featured image
- `content_images` - Images within article content
- `attachments` - Downloadable files and documents

**Automatic Image Processing Features:**
- Thumbnail generation (150x150, 300x300, 500x500)
- Responsive images (sm: 640px, md: 768px, lg: 1024px, xl: 1280px)
- WebP conversion for better performance and smaller file sizes
- Image optimization and compression
- Custom conversions per media collection

**Setup Requirements:**
```bash
# Publish migrations and config
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="medialibrary-migrations"
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="medialibrary-config"

# Run migrations
php artisan migrate
```

**Model Implementation Example:**
```php
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class BusinessUnit extends Model implements HasMedia
{
    use InteractsWithMedia;

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('gallery')
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('logo')
              ->singleFile()
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/svg+xml']);

        $this->addMediaCollection('documents')
              ->acceptsMimeTypes(['application/pdf']);
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
              ->width(300)
              ->height(300)
              ->sharpen(10);

        $this->addMediaConversion('responsive')
              ->width(1200)
              ->optimize()
              ->nonQueued();
    }
}
```

### Phase 6: Advanced Features

#### 6.1 SEO Optimization
- Dynamic meta tags per page
- Sitemap generation
- Open Graph tags
- JSON-LD structured data

#### 6.2 Performance
- Image optimization and lazy loading
- Database query optimization

## File Structure Plan

### Models
```
app/Models/
├── User.php (enhanced with roles)
├── BusinessUnit.php
├── BusinessUnitPage.php
├── BusinessUnitGallery.php
├── GlobalSetting.php
└── NewsArticle.php
```

### Controllers
```
app/Http/Controllers/
├── Admin/
│   ├── DashboardController.php
│   ├── BusinessUnitController.php
│   ├── BusinessUnitPageController.php
│   ├── NewsController.php
│   ├── GlobalSettingController.php
│   └── UserController.php (super admin only)
└── Public/
    ├── HomeController.php
    ├── BusinessUnitController.php
    └── NewsController.php
```

### Frontend Pages
```
resources/js/pages/
├── admin/
│   ├── dashboard.tsx
│   ├── business-units/
│   ├── news/
│   ├── settings/
│   └── users/
└── public/
    ├── home.tsx
    ├── business-unit/
    ├── news/
    └── specialized-pages/
```

### API Endpoints (for admin)
```
/admin/api/business-units
/admin/api/business-units/{id}/pages
/admin/api/business-units/{id}/media
/admin/api/news
/admin/api/news/{id}/media
/admin/api/settings
/admin/api/users (super admin only)
/admin/api/media (global media library)
/admin/api/media/upload
/admin/api/media/{id}/conversions
```

## Implementation Strategy

### 1. Database First
- Create all migrations
- Set up model relationships
- Create factories and seeders

### 2. Authentication Enhancement
- Extend user system with roles
- Create middleware and policies
- Test role-based access

### 3. Admin Interface
- Build admin dashboard
- Create CRUD interfaces
- Implement content management

### 4. Public Interface
- Create dynamic routing
- Build responsive components
- Implement business unit pages

### 5. Testing & Optimization
- Write comprehensive tests
- Optimize performance
- Ensure responsive design

## Success Metrics

- Complete admin content management system
- All UI Text in indonesian language
- Dynamic business unit pages with unique content
- Role-based access control working properly
- Responsive design across all devices
- SEO optimized pages
- Fast loading times
- Comprehensive test coverage

## 🚀 Getting Started

### Prerequisites
- **PHP 8.2+**
- **Node.js 18+**
- **Composer 2.0+**
- **MySQL 8.0+**

### Installation

1. **Clone repository**
```bash
git clone https://github.com/NeddyAP/cigi-global.git
cd cigi-global
```

2. **Install PHP dependencies**
```bash
composer install
```

3. **Install Node.js dependencies**
```bash
npm install
```

4. **Environment setup**
```bash
cp .env.example .env
php artisan key:generate
```

5. **Database setup**
```bash
php artisan migrate
php artisan db:seed
```

6. **Publish Spatie packages**
```bash
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="medialibrary-migrations"
php artisan migrate
```

## 🔧 Available Scripts

```bash
# Development
composer dev              # Run full stack (Laravel + React + Queue)
composer dev:ssr          # Run with SSR support
npm run dev               # Frontend development server only

# Testing
composer test             # Run PHP tests with Pest
npm run test              # Run frontend tests

# Code Quality
composer pint             # Format PHP code with Laravel Pint
npm run lint              # Lint JavaScript/TypeScript
npm run format            # Format frontend code with Prettier

# Build
npm run build             # Build for production
npm run build:ssr         # Build with SSR support
```

## 🎨 Design System

### Color Palette
- **Primary**: Amber (600-700) for CTAs and highlights
- **Neutral**: Zinc (50-900) for backgrounds and text
- **Semantic**: Success (green), Warning (yellow), Error (red), Info (blue)

### Typography Scale
- Responsive typography using `clamp()` for flexible scaling
- Font weights: 400 (regular), 600 (semi-bold), 700 (bold)
- Line heights optimized for readability

### Component Variants
- **Buttons**: Primary, Secondary, Outline, Ghost, Danger
- **Cards**: Default, Hover effects, Glass morphism
- **Badges**: Default, Primary, Success, Warning, Danger, Info

## Next Steps

1. **Immediate**: Design and implement database schema
2. **Short-term**: Build admin authentication and basic CRUD
3. **Medium-term**: Create public-facing business unit pages
4. **Long-term**: Advanced features and optimization

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📞 Contact

**PT Cimande Girang Global**

- **Website**: [cigiglobal.com](https://cigiglobal.com)
- **Email**: <EMAIL>
- **Phone**: +62 21 1234 5678
- **Address**: Cimande, Bogor, West Java, Indonesia

---

Built with ❤️ by Cigi Global Development Team

This plan leverages the existing Laravel React starter kit infrastructure while building a comprehensive company profile website that meets all specified requirements.
