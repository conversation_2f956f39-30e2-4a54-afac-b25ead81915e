[2025-08-22 02:02:13] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14353:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:02:13.238Z"} 
[2025-08-22 02:24:42] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755829481297 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755829481297:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:24:41.568Z"} 
[2025-08-22 02:24:42] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755829481297 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755829481297:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:24:41.569Z"} 
[2025-08-22 02:24:42] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:24:41.570Z"} 
[2025-08-22 02:28:35] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755829715178 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:28:35.428Z"} 
[2025-08-22 02:28:35] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:28:35.428Z"} 
[2025-08-22 02:29:09] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755829748815 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:29:08.948Z"} 
[2025-08-22 02:29:09] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:29:08.948Z"} 
[2025-08-22 02:29:09] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755829748815 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:29:08.952Z"} 
[2025-08-22 02:29:09] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:29:08.952Z"} 
[2025-08-22 02:35:16] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830115131 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830115131:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:35:15.767Z"} 
[2025-08-22 02:35:16] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830115131 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830115131:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:35:15.767Z"} 
[2025-08-22 02:35:16] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:35:15.769Z"} 
[2025-08-22 02:41:06] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830464215 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830464215:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:360:117
performReactRefresh/<@http://[::1]:5173/@react-refresh:208:17
performReactRefresh@http://[::1]:5173/@react-refresh:190:25
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:05.686Z"} 
[2025-08-22 02:41:06] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830464215 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830464215:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:360:117
performReactRefresh/<@http://[::1]:5173/@react-refresh:208:17
performReactRefresh@http://[::1]:5173/@react-refresh:190:25
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:05.687Z"} 
[2025-08-22 02:41:06] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:05.688Z"} 
[2025-08-22 02:41:17] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830474655 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830474655:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:360:117
performReactRefresh/<@http://[::1]:5173/@react-refresh:208:17
performReactRefresh@http://[::1]:5173/@react-refresh:190:25
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:15.898Z"} 
[2025-08-22 02:41:17] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830474655 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830474655:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:360:117
performReactRefresh/<@http://[::1]:5173/@react-refresh:208:17
performReactRefresh@http://[::1]:5173/@react-refresh:190:25
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:15.898Z"} 
[2025-08-22 02:41:17] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:15.898Z"} 
[2025-08-22 02:41:29] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:360:117
performReactRefresh/<@http://[::1]:5173/@react-refresh:208:17
performReactRefresh@http://[::1]:5173/@react-refresh:190:25
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:28.245Z"} 
[2025-08-22 02:41:29] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:360:117
performReactRefresh/<@http://[::1]:5173/@react-refresh:208:17
performReactRefresh@http://[::1]:5173/@react-refresh:190:25
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:28.245Z"} 
[2025-08-22 02:41:29] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:41:28.245Z"} 
[2025-08-22 02:44:10] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:44:10.180Z"} 
[2025-08-22 02:44:10] local.ERROR: TypeError: can't access property "length", featured_gallery is undefined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781 473 9 TypeError can't access property "length", featured_gallery is undefined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755830487781:473:9
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:44:10.180Z"} 
[2025-08-22 02:44:10] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:44:10.181Z"} 
[2025-08-22 02:46:42] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:46:42.083Z"} 
[2025-08-22 02:46:43] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:46:42.085Z"} 
[2025-08-22 02:47:58] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873443 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:47:58.385Z"} 
[2025-08-22 02:47:58] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873316 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:47:58.452Z"} 
[2025-08-22 02:47:58] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:47:58.385Z"} 
[2025-08-22 02:47:58] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:47:58.454Z"} 
[2025-08-22 02:48:01] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873444 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:00.673Z"} 
[2025-08-22 02:48:01] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:00.673Z"} 
[2025-08-22 02:48:04] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873569 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:02.990Z"} 
[2025-08-22 02:48:04] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:02.990Z"} 
[2025-08-22 02:48:06] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873570 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:05.308Z"} 
[2025-08-22 02:48:06] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:05.308Z"} 
[2025-08-22 02:48:08] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873574 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:07.590Z"} 
[2025-08-22 02:48:08] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:07.590Z"} 
[2025-08-22 02:48:10] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873697 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:09.893Z"} 
[2025-08-22 02:48:10] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:09.893Z"} 
[2025-08-22 02:48:14] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873966 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:11.947Z"} 
[2025-08-22 02:48:14] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:11.947Z"} 
[2025-08-22 02:48:14] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/components/public/public-footer.tsx?t=1755830873966 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:12.974Z"} 
[2025-08-22 02:48:14] local.ERROR: [vite] Failed to reload /resources/js/components/public/public-footer.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:12.975Z"} 
[2025-08-22 02:48:16] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/components/public/public-navigation.tsx?t=1755830873967 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:15.037Z"} 
[2025-08-22 02:48:16] local.ERROR: [vite] Failed to reload /resources/js/components/public/public-navigation.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:15.037Z"} 
[2025-08-22 02:48:17] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873967 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:16.107Z"} 
[2025-08-22 02:48:17] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:16.107Z"} 
[2025-08-22 02:48:19] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873968 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:18.396Z"} 
[2025-08-22 02:48:19] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:18.396Z"} 
[2025-08-22 02:48:21] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873969 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:20.452Z"} 
[2025-08-22 02:48:21] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:20.452Z"} 
[2025-08-22 02:48:22] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/components/public/public-header.tsx?t=1755830873969 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:21.508Z"} 
[2025-08-22 02:48:22] local.ERROR: [vite] Failed to reload /resources/js/components/public/public-header.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:21.508Z"} 
[2025-08-22 02:48:24] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830874133 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:23.824Z"} 
[2025-08-22 02:48:24] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:23.824Z"} 
[2025-08-22 02:48:27] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830874480 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:26.144Z"} 
[2025-08-22 02:48:27] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:26.144Z"} 
[2025-08-22 02:48:29] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830874481 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:28.203Z"} 
[2025-08-22 02:48:29] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:28.203Z"} 
[2025-08-22 02:48:29] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/layouts/public-layout.tsx?t=1755830874481 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:28.449Z"} 
[2025-08-22 02:48:29] local.ERROR: [vite] Failed to reload /resources/js/layouts/public-layout.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:28.449Z"} 
[2025-08-22 02:48:34] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873322 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:34.620Z"} 
[2025-08-22 02:48:34] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:34.620Z"} 
[2025-08-22 02:48:38] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873323 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:36.960Z"} 
[2025-08-22 02:48:38] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:36.960Z"} 
[2025-08-22 02:48:40] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873442 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:39.256Z"} 
[2025-08-22 02:48:40] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:39.257Z"} 
[2025-08-22 02:48:42] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873443 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:41.570Z"} 
[2025-08-22 02:48:42] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:41.571Z"} 
[2025-08-22 02:48:44] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873444 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:43.895Z"} 
[2025-08-22 02:48:44] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:43.896Z"} 
[2025-08-22 02:48:47] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873569 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:46.198Z"} 
[2025-08-22 02:48:47] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:46.198Z"} 
[2025-08-22 02:48:49] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873570 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:48.498Z"} 
[2025-08-22 02:48:49] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:48.498Z"} 
[2025-08-22 02:48:51] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873574 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:50.829Z"} 
[2025-08-22 02:48:51] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:50.829Z"} 
[2025-08-22 02:48:54] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873697 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:53.121Z"} 
[2025-08-22 02:48:54] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:53.122Z"} 
[2025-08-22 02:48:56] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873966 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:55.441Z"} 
[2025-08-22 02:48:56] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:55.442Z"} 
[2025-08-22 02:48:58] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873967 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:57.765Z"} 
[2025-08-22 02:48:58] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:48:57.765Z"} 
[2025-08-22 02:49:01] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873968 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:00.045Z"} 
[2025-08-22 02:49:01] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:00.045Z"} 
[2025-08-22 02:49:03] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830873969 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:02.381Z"} 
[2025-08-22 02:49:03] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:02.381Z"} 
[2025-08-22 02:49:05] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830874133 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:04.685Z"} 
[2025-08-22 02:49:05] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:04.686Z"} 
[2025-08-22 02:49:08] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830874480 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:07.004Z"} 
[2025-08-22 02:49:08] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:07.004Z"} 
[2025-08-22 02:49:10] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/css/app.css?t=1755830874481 null {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:09.298Z"} 
[2025-08-22 02:49:10] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:09.299Z"} 
[2025-08-22 02:49:17] local.ERROR: SyntaxError: redeclaration of let exceptionAsMarkdown http://cigi-global.test/admin/business-units 5323 25 SyntaxError redeclaration of let exceptionAsMarkdown @http://cigi-global.test/admin/business-units:5323:25 {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:15.161Z"} 
[2025-08-22 02:49:17] local.ERROR: SyntaxError: redeclaration of let exceptionAsMarkdown http://cigi-global.test/admin/business-units 5323 25 SyntaxError redeclaration of let exceptionAsMarkdown @http://cigi-global.test/admin/business-units:5323:25 {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:15.161Z"} 
[2025-08-22 02:49:17] local.ERROR: SyntaxError: redeclaration of let pi http://cigi-global.test/admin/business-units 6601 13 SyntaxError redeclaration of let pi @http://cigi-global.test/admin/business-units:6601:13 {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:15.192Z"} 
[2025-08-22 02:49:17] local.ERROR: SyntaxError: redeclaration of let pi http://cigi-global.test/admin/business-units 6601 13 SyntaxError redeclaration of let pi @http://cigi-global.test/admin/business-units:6601:13 {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T02:49:15.192Z"} 
