<?php

use App\Http\Controllers\Admin\BusinessUnitController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\NewsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group and require authentication.
|
*/

Route::middleware(['auth', 'role:admin|super-admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/', DashboardController::class)->name('dashboard');
    Route::get('/dashboard', DashboardController::class)->name('dashboard.index');

    // Business Units Management
    Route::resource('business-units', BusinessUnitController::class);

    // News Management
    Route::resource('news', NewsController::class);

    // User Management (Super Admin only)
    Route::middleware('role:super-admin')->group(function () {
        Route::get('/users', function () {
            return inertia('admin/users/index');
        })->name('users.index');
    });

    // Global Settings
    Route::get('/settings', function () {
        return inertia('admin/settings/index');
    })->name('settings.index');
});
