<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BusinessUnit;
use App\Models\NewsArticle;
use App\Models\User;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    public function __invoke(): Response
    {
        $this->authorize('view business units');

        $stats = [
            'business_units' => [
                'total' => BusinessUnit::count(),
                'active' => BusinessUnit::where('status', 'active')->count(),
                'coming_soon' => BusinessUnit::where('status', 'coming_soon')->count(),
            ],
            'news' => [
                'total' => NewsArticle::count(),
                'published' => NewsArticle::where('status', 'published')->count(),
                'draft' => NewsArticle::where('status', 'draft')->count(),
            ],
            'users' => [
                'total' => User::count(),
                'admins' => User::role(['admin', 'super-admin'])->count(),
            ],
        ];

        $recent_news = NewsArticle::with('author')
            ->latest()
            ->limit(5)
            ->get();

        $business_units = BusinessUnit::ordered()->get();

        return Inertia::render('admin/dashboard', [
            'stats' => $stats,
            'recent_news' => $recent_news,
            'business_units' => $business_units,
        ]);
    }
}
